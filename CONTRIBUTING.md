# Đóng Góp Cho Đớp Phim

Cảm ơn bạn đã quan tâm đến việc đóng góp cho Đớp Phim! Tài liệu này cung cấp hướng dẫn và thông tin cho các contributors.

## 🤝 Cách Đóng Góp

### Báo Cáo Lỗi

1. **Tì<PERSON> kiếm các issues hiện có** trước để tránh trùng lặp
2. **Sử dụng template issue** khi tạo issues mới
3. **Cung cấp thông tin chi tiết** bao gồm:
   - Các bước để tái tạo lỗi
   - Hành vi mong đợi vs thực tế
   - Screenshots hoặc thông báo lỗi
   - Thông tin thiết bị/platform
   - Phiên bản ứng dụng

### Đề Xuất Tính Năng

1. **Kiểm tra các feature requests hiện có** để tránh trùng lặp
2. **<PERSON><PERSON> tả tính năng** rõ ràng và cung cấp use cases
3. **<PERSON><PERSON><PERSON><PERSON> thích lợi ích** và tác động tiềm năng
4. **Xem xét độ phức tạp triển khai** và các phương án thay thế

### Đóng Góp Code

#### Yêu Cầu Tiên Quyết

- Flutter SDK (>=2.16.2 <3.0.0)
- Kiến thức về Git
- Hiểu biết về phát triển Dart/Flutter
- Kinh nghiệm Firebase (ưu tiên)

#### Thiết Lập Môi Trường Phát Triển

1. **Fork repository**
2. **Clone fork của bạn**
   ```bash
   git clone https://github.com/dai98tb/dop-phim-movie-booking.git
   cd dop-phim-movie-booking
   ```
3. **Tạo feature branch**
   ```bash
   git checkout -b feature/ten-tinh-nang-cua-ban
   ```
4. **Cài đặt dependencies**
   ```bash
   flutter pub get
   ```
5. **Thiết lập Firebase** (xem README.md để biết hướng dẫn chi tiết)

#### Tiêu Chuẩn Coding

##### Phong Cách Code
- Tuân theo [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Sử dụng tên biến và function có ý nghĩa
- Thêm comments cho logic phức tạp
- Giữ functions nhỏ và tập trung
- Sử dụng indentation đúng (2 spaces)

##### Best Practices Flutter
- Sử dụng `const` constructors khi có thể
- Triển khai state management đúng cách với GetX
- Tuân theo Material Design guidelines
- Đảm bảo responsive design cho các kích thước màn hình khác nhau
- Tối ưu hiệu suất và sử dụng bộ nhớ

##### Tổ Chức File
- Đặt files vào thư mục phù hợp
- Sử dụng tên file mô tả rõ ràng
- Nhóm các chức năng liên quan lại với nhau
- Tuân theo cấu trúc dự án hiện có

#### Kiểm Thử

1. **Viết tests** cho các tính năng mới và bug fixes
2. **Chạy existing tests** để đảm bảo không có regression
   ```bash
   flutter test
   ```
3. **Test trên nhiều thiết bị** và kích thước màn hình
4. **Xác minh Firebase integration** hoạt động đúng

#### Quy Trình Pull Request

1. **Cập nhật documentation** nếu cần
2. **Thêm/cập nhật tests** cho các thay đổi của bạn
3. **Đảm bảo tất cả tests pass**
4. **Tuân theo PR template**
5. **Yêu cầu review** từ maintainers

##### Hướng Dẫn PR
- **Tiêu đề rõ ràng** mô tả thay đổi
- **Mô tả chi tiết** về những gì đã thay đổi và tại sao
- **Liên kết related issues** sử dụng keywords (fixes #123)
- **Bao gồm screenshots** cho UI changes
- **Giữ PRs tập trung** - một feature/fix mỗi PR

## 🏗 Project Architecture

### Directory Structure
```
lib/
├── bindings/          # GetX dependency injection
├── config/           # App configuration and constants
├── controllers/      # GetX controllers for state management
├── models/          # Data models and entities
├── services/        # API services and Firebase integration
├── utils/           # Utility functions and helpers
├── view/            # UI screens and pages
├── widgets/         # Reusable UI components
└── translations/    # Internationalization files
```

### Key Technologies
- **State Management**: GetX
- **Backend**: Firebase (Firestore, Auth, Storage, Functions)
- **Payment**: PayPal SDK
- **Video**: Media Kit
- **UI**: Material Design with custom theming

## 🔒 Security Guidelines

### Sensitive Information
- **Never commit** API keys, passwords, or secrets
- **Use environment variables** for configuration
- **Follow Firebase security rules** best practices
- **Validate all user inputs** on both client and server

### Code Security
- **Sanitize data** before database operations
- **Implement proper authentication** checks
- **Use HTTPS** for all network requests
- **Follow OWASP mobile security guidelines**

## 🐛 Bug Reports

### Before Reporting
1. **Update to latest version**
2. **Check existing issues**
3. **Try to reproduce** the issue consistently

### Bug Report Template
```
**Bug Description**
A clear description of the bug.

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Screenshots**
If applicable, add screenshots.

**Environment**
- Device: [e.g. iPhone 12, Samsung Galaxy S21]
- OS: [e.g. iOS 15.0, Android 11]
- App Version: [e.g. 1.0.0]
- Flutter Version: [e.g. 3.0.0]
```

## 📝 Documentation

### Code Documentation
- **Document public APIs** with clear descriptions
- **Include examples** for complex functions
- **Update README.md** for significant changes
- **Maintain inline comments** for complex logic

### User Documentation
- **Update user guides** for new features
- **Include screenshots** for UI changes
- **Provide setup instructions** for new dependencies
- **Maintain troubleshooting guides**

## 🎯 Feature Development

### Planning Phase
1. **Discuss the feature** in an issue first
2. **Get approval** from maintainers
3. **Plan the implementation** approach
4. **Consider impact** on existing features

### Implementation Phase
1. **Create feature branch** from main
2. **Implement incrementally** with regular commits
3. **Test thoroughly** during development
4. **Update documentation** as needed

### Review Phase
1. **Self-review** your code before submitting
2. **Address feedback** promptly and professionally
3. **Make requested changes** in additional commits
4. **Squash commits** if requested

## 🚀 Release Process

### Version Numbering
- Follow [Semantic Versioning](https://semver.org/)
- **Major**: Breaking changes
- **Minor**: New features (backward compatible)
- **Patch**: Bug fixes (backward compatible)

### Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version number bumped
- [ ] Changelog updated
- [ ] Firebase rules deployed
- [ ] App tested on multiple devices

## 📞 Getting Help

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Code Reviews**: Pull request discussions

### Response Times
- **Issues**: We aim to respond within 48 hours
- **Pull Requests**: Initial review within 72 hours
- **Security Issues**: Immediate attention (email maintainers)

## 📜 Code of Conduct

### Our Standards
- **Be respectful** and inclusive
- **Provide constructive feedback**
- **Focus on the code**, not the person
- **Help others learn** and grow
- **Follow project guidelines**

### Unacceptable Behavior
- Harassment or discrimination
- Trolling or insulting comments
- Publishing private information
- Spam or off-topic discussions

## 🙏 Recognition

Contributors will be recognized in:
- **README.md** contributors section
- **Release notes** for significant contributions
- **GitHub contributors** page

Thank you for contributing to Đớp Phim! 🎬
