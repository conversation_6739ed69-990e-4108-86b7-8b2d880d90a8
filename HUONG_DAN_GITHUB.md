# Hướng Dẫn Đẩy Dự Án Lên GitHub

Tài liệu này hướng dẫn chi tiết cách đẩy dự án "Đớ<PERSON> Phim" lên GitHub một cách an toàn và chuyên nghiệp.

## 📋 Chuẩn Bị Trước Khi Đẩy

### 1. Kiểm Tra Files Nhạy Cảm

Đảm bảo các files sau **KHÔNG** được commit:

```bash
# Kiểm tra .gitignore đã bao gồm:
firebase_options.dart
google-services.json
GoogleService-Info.plist
android/key.properties
android/local.properties
.env
```

### 2. Xóa Dữ Liệu Nhạy Cảm

Nếu đã commit nhầm files nhạy cảm, xóa khỏi Git history:

```bash
# Xóa file khỏi Git history
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch lib/firebase_options.dart' \
--prune-empty --tag-name-filter cat -- --all

# Hoặc sử dụng BFG Repo-Cleaner (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị)
java -jar bfg.jar --delete-files firebase_options.dart
```

### 3. Tạo Firebase Options Template

Tạo file template cho Firebase configuration:

```dart
// lib/firebase_options_template.dart
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    // TODO: Thay thế bằng Firebase configuration thực tế
    throw UnsupportedError(
      'DefaultFirebaseOptions chưa được cấu hình cho platform này.'
    );
  }
}
```

## 🚀 Các Bước Đẩy Lên GitHub

### Bước 1: Khởi Tạo Git Repository

```bash
# Di chuyển vào thư mục dự án
cd e:\Movie_Finder-Flutter--main

# Khởi tạo Git (nếu chưa có)
git init

# Thêm tất cả files
git add .

# Commit đầu tiên
git commit -m "🎬 Initial commit: Ứng dụng đặt vé xem phim Đớp Phim

- Hệ thống đặt vé hoàn chỉnh với Firebase
- Tích hợp PayPal cho thanh toán
- Thông báo thời gian thực
- Quản lý rạp và lịch chiếu
- Giao diện tiếng Việt thân thiện"
```

### Bước 2: Tạo Repository Trên GitHub

1. **Đăng nhập GitHub** và tạo repository mới
2. **Tên repository**: `dop-phim-movie-booking` hoặc `movie-finder-flutter`
3. **Mô tả**: "Ứng dụng đặt vé xem phim Flutter với Firebase và PayPal"
4. **Chọn Public** (hoặc Private nếu muốn)
5. **KHÔNG** tích "Initialize with README" (vì đã có sẵn)

### Bước 3: Kết Nối Với GitHub

```bash
# Thêm remote origin
git remote add origin https://github.com/yourusername/dop-phim-movie-booking.git

# Đổi tên branch chính thành main
git branch -M main

# Đẩy lên GitHub lần đầu
git push -u origin main
```

### Bước 4: Cấu Hình Repository

#### Thiết Lập Branch Protection

1. Vào **Settings** > **Branches**
2. Thêm rule cho branch `main`:
   - ✅ Require pull request reviews
   - ✅ Require status checks to pass
   - ✅ Require branches to be up to date

#### Cấu Hình GitHub Actions Secrets

Vào **Settings** > **Secrets and variables** > **Actions**:

```
FIREBASE_TOKEN=your_firebase_token
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_SECRET=your_paypal_secret
```

#### Bật GitHub Features

1. **Issues**: Bật để nhận bug reports
2. **Projects**: Bật để quản lý tasks
3. **Wiki**: Bật để tài liệu bổ sung
4. **Discussions**: Bật để thảo luận cộng đồng

## 📁 Cấu Trúc Repository Sau Khi Đẩy

```
dop-phim-movie-booking/
├── .github/
│   ├── workflows/
│   │   └── ci.yml                    # CI/CD pipeline
│   ├── ISSUE_TEMPLATE/
│   │   ├── bug_report.md            # Template báo lỗi
│   │   └── feature_request.md       # Template đề xuất tính năng
│   └── pull_request_template.md     # Template pull request
├── android/                         # Android configuration
├── ios/                            # iOS configuration
├── lib/                            # Source code chính
├── assets/                         # Hình ảnh và tài nguyên
├── functions/                      # Firebase Functions
├── .gitignore                      # Files bị bỏ qua
├── README.md                       # Tài liệu chính (tiếng Việt)
├── CONTRIBUTING.md                 # Hướng dẫn đóng góp
├── LICENSE                         # Giấy phép MIT
├── CHANGELOG.md                    # Lịch sử thay đổi
├── SECURITY.md                     # Chính sách bảo mật
├── HUONG_DAN_GITHUB.md            # File này
└── pubspec.yaml                    # Dependencies Flutter
```

## 🔒 Bảo Mật Repository

### 1. Cấu Hình Security Alerts

1. Vào **Settings** > **Security & analysis**
2. Bật:
   - ✅ Dependency graph
   - ✅ Dependabot alerts
   - ✅ Dependabot security updates

### 2. Thiết Lập Code Scanning

```yaml
# .github/workflows/security.yml
name: Security Scan
on: [push, pull_request]
jobs:
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
```

### 3. Bảo Vệ Sensitive Data

```bash
# Thêm vào .gitignore
echo "# Sensitive files" >> .gitignore
echo "*.key" >> .gitignore
echo "*.p12" >> .gitignore
echo "*.jks" >> .gitignore
echo ".env*" >> .gitignore
```

## 📊 Theo Dõi Và Quản Lý

### 1. Thiết Lập GitHub Projects

1. Tạo **Project** mới với template "Basic Kanban"
2. Thêm columns:
   - 📋 Backlog
   - 🔄 In Progress
   - 👀 In Review
   - ✅ Done

### 2. Tạo Issues Templates

Đã có sẵn trong `.github/ISSUE_TEMPLATE/`:
- `bug_report.md`: Báo cáo lỗi
- `feature_request.md`: Đề xuất tính năng

### 3. Thiết Lập Milestones

Tạo milestones cho các phiên bản:
- 🎯 v1.0.0 - Release đầu tiên
- 🚀 v1.1.0 - Tính năng mới
- 🐛 v1.0.1 - Bug fixes

## 🔄 Workflow Phát Triển

### 1. Feature Development

```bash
# Tạo branch mới cho tính năng
git checkout -b feature/ten-tinh-nang

# Phát triển và commit
git add .
git commit -m "✨ Thêm tính năng mới"

# Đẩy lên GitHub
git push origin feature/ten-tinh-nang

# Tạo Pull Request trên GitHub
```

### 2. Bug Fixes

```bash
# Tạo branch cho bug fix
git checkout -b fix/ten-loi

# Sửa lỗi và commit
git add .
git commit -m "🐛 Sửa lỗi: mô tả lỗi"

# Đẩy và tạo PR
git push origin fix/ten-loi
```

### 3. Release Process

```bash
# Cập nhật version trong pubspec.yaml
# Cập nhật CHANGELOG.md
# Commit changes
git add .
git commit -m "🔖 Release v1.0.0"

# Tạo tag
git tag -a v1.0.0 -m "Release version 1.0.0"

# Đẩy tag lên GitHub
git push origin v1.0.0
```

## 📱 Cấu Hình CI/CD

### 1. Flutter Build Pipeline

File `.github/workflows/ci.yml` đã được tạo với:
- ✅ Kiểm tra code formatting
- ✅ Chạy tests
- ✅ Build Android APK
- ✅ Build iOS (trên macOS)
- ✅ Security scanning

### 2. Firebase Deployment

```yaml
# Thêm vào CI pipeline
- name: Deploy Firebase Functions
  run: |
    npm install -g firebase-tools
    cd functions && npm install
    firebase deploy --only functions
  env:
    FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
```

## 🎯 Best Practices

### 1. Commit Messages

Sử dụng format:
```
🎨 :art: Cải thiện cấu trúc/format code
🐛 :bug: Sửa lỗi
✨ :sparkles: Tính năng mới
📝 :memo: Thêm/cập nhật documentation
🚀 :rocket: Deploy/release
🔒 :lock: Sửa lỗi bảo mật
```

### 2. Branch Naming

```
feature/ten-tinh-nang
fix/ten-loi
hotfix/loi-khan-cap
release/v1.0.0
```

### 3. PR Guidelines

- Tiêu đề rõ ràng
- Mô tả chi tiết thay đổi
- Link đến issues liên quan
- Screenshots cho UI changes
- Checklist đầy đủ

## 🆘 Xử Lý Sự Cố

### 1. Nếu Commit Nhầm Sensitive Data

```bash
# Xóa file khỏi staging
git reset HEAD file-name

# Xóa khỏi Git history
git filter-branch --index-filter \
'git rm --cached --ignore-unmatch file-name'

# Force push (cẩn thận!)
git push --force-with-lease origin main
```

### 2. Nếu Cần Đổi Tên Repository

1. Vào **Settings** > **General**
2. Scroll xuống **Repository name**
3. Đổi tên và click **Rename**
4. Cập nhật remote URL:

```bash
git remote set-url origin https://github.com/username/new-repo-name.git
```

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra [GitHub Docs](https://docs.github.com)
2. Tạo issue trong repository
3. Liên hệ team phát triển

---

**Chúc mừng! 🎉 Dự án Đớp Phim của bạn đã sẵn sàng trên GitHub!**
